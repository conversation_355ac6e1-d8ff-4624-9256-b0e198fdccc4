/* 颜色变量 */
:root {
    /* 主色调 */
    --primary-color: #FFD300;
    --primary-light: #FFDA33;
    --primary-dark: #E6BE00;

    /* 文本颜色 */
    --text-primary: #333333;
    --text-secondary: #6c757d;
    --text-light: #f8f9fa;
    --text-dark: #212529;
    --text-muted: #6c757d;

    /* 背景颜色 */
    --bg-light: #FFFFFF;
    --bg-dark: #121212;
    --bg-light-secondary: #f8f9fa;
    --bg-dark-secondary: #1e1e1e;

    /* 灰度颜色 */
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;

    /* 状态颜色 */
    --success: #28a745;
    --info: #17a2b8;
    --warning: #ffc107;
    --danger: #dc3545;

    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* 边框圆角 */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 1rem;
    --border-radius-xl: 1.5rem;
    --border-radius-full: 9999px;

    /* 字体大小 */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.25rem;
    --font-size-xl: 1.5rem;
    --font-size-2xl: 2rem;
    --font-size-3xl: 2.5rem;
    --font-size-4xl: 3rem;

    /* 字体粗细 */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* 行高 */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    --line-height-loose: 2;

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* 过渡 */
    --transition-fast: 150ms;
    --transition-normal: 300ms;
    --transition-slow: 500ms;

    /* 响应式断点 */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;

    /* 按钮样式 */
    --btn-padding-x: 1rem;
    --btn-padding-y: 0.5rem;
    --btn-padding-x-sm: 0.75rem;
    --btn-padding-y-sm: 0.375rem;
    --btn-padding-x-lg: 1.5rem;
    --btn-padding-y-lg: 0.75rem;
    --btn-border-radius: 0.375rem;
    --btn-border-radius-sm: 0.25rem;
    --btn-border-radius-lg: 0.5rem;
    --btn-border-width: 1px;
    --btn-font-weight: 500;
    --btn-transition: all 0.3s ease;

    /* 卡片样式 */
    --card-border-radius: 0.75rem;
    --card-padding: 1.5rem;
    --card-bg-light: white;
    --card-bg-dark: #1f2937;
    --card-border-color-light: #e5e7eb;
    --card-border-color-dark: #374151;
    --card-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    --card-shadow-hover: 0 6px 12px rgba(0, 0, 0, 0.1);
    --card-transition: all 0.3s ease;
}
